# Simple WGPU 2D Game Engine - Detailed Plan

## Project Architecture

### Project Structure
```
simple_wgpu/
├── engine/                    # Dynamic library crate
│   ├── src/
│   │   ├── lib.rs            # Main library entry point
│   │   ├── core/             # Core engine initialization
│   │   ├── window/           # Window management (winit)
│   │   ├── settings/         # Engine settings management
│   │   ├── asset/            # Asset loading and management
│   │   ├── render/           # Rendering system (wgpu)
│   │   └── utils/            # Utility functions
│   └── Cargo.toml
├── game/                     # Example game executable
│   ├── src/
│   │   └── main.rs
│   └── Cargo.toml
└── assets/                   # Asset directory
    └── sprites/
```

### Dynamic Library Design
- Engine compiled as `cdylib` for Rust-to-Rust dynamic library
- Expose Rust-native API with proper types and error handling
- Internal implementation with safe abstractions
- Game executable links to engine dynamically via Rust FFI

## Core Modules

### 1. Core Module (`engine.core`)
**Purpose**: Engine initialization and lifecycle management
**Functions**:
- `init()`: Initialize wgpu device, surface, and all subsystems
- Internal state management for engine lifecycle

**Implementation Details**:
- Initialize wgpu instance with Vulkan/DX12/Metal backends
- Create logical device and command queue
- Set up surface for rendering
- Initialize all other modules in correct order

### 2. Window Module (`engine.window`)
**Purpose**: Window creation and management using winit
**Functions**:
- `new(title: string, id: int)`: Create new window with specified title and ID
- Single window maximum support

**Implementation Details**:
- Use winit EventLoop for cross-platform window management
- Handle window events (resize, close, etc.)
- Runtime window resizing with resolution preservation
- Nearest neighbor scaling when window size > target resolution
- Integrate with wgpu surface creation
- Maintain aspect ratio and scaling calculations

### 3. Settings Module (`engine.settings`)
**Purpose**: Runtime configuration management
**Functions**:
- `set(vsync_type, fullscreen_type, fps_limit, res_x, res_y)`
- `get()`: Return current settings

**Settings Supported**:
- VSync: On/Off/Adaptive
- Fullscreen: Windowed/Borderless/Exclusive
- FPS Limit: Uncapped/30/60/120/Custom
- Resolution: Width x Height

**Implementation Details**:
- Hot-swappable settings without restart
- Validate settings against hardware capabilities
- Persist settings to config file

### 4. Asset Module (`engine.asset`)
**Purpose**: Asset loading and management
**Functions**:
- `load_sprite(filename: string, sprite_name: string)`: Load sprite from assets folder
- `load_font(filename: string, font_name: string)`: Load TTF font from assets folder

**Implementation Details**:
- Automatic asset folder detection in current directory
- Support for PNG, JPG, BMP, WEBP image formats
- TTF font loading and management
- Automatic texture atlas generation with multiple popular algorithms:
  - Rectangle packing (MaxRects, Skyline, Guillotine)
  - Power-of-2 atlas sizes for GPU compatibility
  - Automatic optimization when atlas gets fragmented
- Hot-reloading in development mode with file system watching (images only)
- GPU texture upload and management
- Asset caching and reference counting

### 5. Render Module (`engine.render`)
**Purpose**: High-performance 2D rendering
**Functions**:
- `start()`: Begin frame rendering
- `stop()`: End frame and present
- `cleardepth()`: Clear depth buffer
- `clearcolor()`: Clear color buffer  
- `sprite(sprite_name: string, x: f32, z: f32)`: Draw sprite at normalized position (0.0-1.0)
- `text(text: string, font_name: string, x: f32, z: f32)`: Draw text at normalized position (0.0-1.0)
- `get_delta_time()`: Get frame delta time in seconds
- `get_fps()`: Get current FPS

**Implementation Details**:
- Batch rendering for optimal performance
- Instanced rendering for sprites
- Z-ordering for depth sorting (0.0 = front, 1.0 = back)
- Normalized coordinate system (0.0-1.0) with automatic scaling
- Coordinate system: (0,0) = top-left, (1,1) = bottom-right
- Window resizing support with resolution preservation
- Nearest neighbor upscaling when window > target resolution
- Efficient command buffer recording
- Automatic texture binding optimization
- TTF font rendering with Unicode support (no styling)
- Text rendered as textured quads with font atlas
- Delta time and FPS tracking
- FPS counter display in development mode

## Performance Optimizations

### Rendering Performance
- Batch all draw calls per frame
- Use instanced rendering for sprites
- Minimize state changes
- Texture atlas to reduce bind operations
- Command buffer reuse where possible

### Memory Management
- Object pooling for frequently created/destroyed objects
- Efficient vertex buffer management
- Texture streaming for large assets
- Reference counting for asset management

### CPU Performance
- Minimize allocations in hot paths
- Use SIMD where applicable
- Multi-threaded asset loading
- Efficient spatial data structures

## Cross-Platform Considerations

### Windows Support
- DirectX 12 backend via wgpu
- Windows-specific optimizations
- Proper DLL export/import

### Linux Support  
- Vulkan backend via wgpu
- X11/Wayland compatibility via winit
- Shared library (.so) format

### Build System
- Cargo workspace for multi-crate project
- Cross-compilation support
- Automated testing on both platforms

## API Design Philosophy

### Simplicity
- Hide all wgpu/winit complexity
- No manual resource management required
- Automatic error handling with sensible defaults
- Minimal required function calls

### Performance
- All operations designed for 60+ FPS
- Automatic batching and optimization
- Lazy loading where beneficial
- Efficient default behaviors

### Flexibility
- Modular design allows selective usage
- Settings can be changed at runtime
- Multiple windows supported
- Extensible asset system

## Implementation Phases

### Phase 1: Core Infrastructure
- Basic wgpu setup and window creation
- Core module initialization
- Basic settings management

### Phase 2: Asset System
- Image loading and texture creation
- Asset folder management
- Basic sprite rendering

### Phase 3: Rendering System
- Batch rendering implementation
- Text rendering support
- Z-ordering and depth management

### Phase 4: Performance & Polish
- Optimization passes
- Cross-platform testing
- API refinement
- Documentation

## Technical Dependencies

### Core Dependencies
- wgpu: Graphics API abstraction
- winit: Cross-platform windowing
- image: Image loading and processing
- bytemuck: Safe transmutation for GPU data

### Additional Dependencies
- glam: Mathematics library for 2D operations
- pollster: Async runtime for wgpu
- env_logger: Logging for development
- serde: Settings serialization
- fontdue: TTF font rendering with Unicode support
- notify: File system watching for hot-reloading
- rectangle-pack: Texture atlas generation algorithms

## Error Handling Strategy
- Silent failures with comprehensive logging for non-critical errors
- Critical errors display error window and exit gracefully
- Graceful degradation on hardware limitations
- Clear error messages for common issues
- Automatic fallbacks for unsupported features
- Debug logging for development mode

## Development vs Release Modes

### Development Mode Features
- Hot-reloading of image assets only (not fonts)
- File system watching for automatic asset updates
- FPS counter display
- Detailed debug logging and performance metrics
- Error overlay for debugging
- Asset reload notifications

### Release Mode Features
- Optimized asset loading (no file watching)
- Minimal logging overhead
- Production error handling
- Optimized texture atlas generation

## Future Extensibility
- Plugin system architecture
- Scripting language integration potential
- Audio system integration points
- Physics system integration points
